<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px" style="margin-left: 50px; position: relative;">
            <!-- 第一行 - 单位属性模块 -->
            <el-form-item label="时间" prop="dateRange">
                <!-- 时间选择器或其他输入组件 -->
            </el-form-item>
            <el-form-item style="position: absolute; right: 50px;">
                <el-button type="primary" icon="Search" @click="handleQuery">筛选分析</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form> <!-- 在此处闭合 el-form 标签 -->

        <el-table>
            <el-table-column label="时间" width="160" align="center"></el-table-column>
            <el-table-column label="名称" width="160" align="center"></el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
                               v-hasPermi="['system:audio:detail']">详情
                    </el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                               v-hasPermi="['system:audio:remove']">删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getFaxuanData"
        />
    </div>
</template>


<script setup name="Audio">
import {getToken} from "@/utils/auth"
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { getFilterArticles, getFilterArticlesSum, getFilterArticlesDetail } from '@/api/business/faxuan'

const {proxy} = getCurrentInstance()

const tableRefs = ref(null)
const audioList = ref([])
const faxuanData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const uploadDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const fileList = ref([])
const dateRange = ref([])
const total = ref(0)
const selectedAudios = ref([])
const audioPlayerVisible = ref(false)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const progressValue = ref(0)
const isDragging = ref(false)
let currentAudio = null

// 上传配置
const uploadAction = ref(import.meta.env.VITE_APP_BASE_API + '/swapi/audio/files/upload')
const uploadHeaders = ref({
    Authorization: 'Bearer ' + getToken()
})

const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        file_name: undefined,
        user_name: undefined,
        start_time: undefined,
        end_time: undefined,
        min_score: undefined,
        max_score: undefined,
        unitType: undefined,
        industry_system: undefined,
        source: undefined,
        laws: undefined,
        people_num: undefined,
        form: undefined,
        accept_people: undefined
    }
})

// 模块选择状态：'unit' 表示选择了单位属性模块，'industry' 表示选择了行业系统模块
const moduleSelection = ref('')

// 控制单选按钮显示状态
const showRadioButtons = ref(false)

// 下拉框显示文本
const selectedUnitTypeDisplay = ref('')

// 保存上次筛选分析时的参数
const lastFilterParams = ref({
    unit_property: "",
    industry_system: "",
    publish_time_start: "",
    publish_time_end: "",
    crawl_channel: "",
    legal_content_type: "",
    people_scale: "",
    target_group: ""
})

const {queryParams} = toRefs(data)

/** 查询音频列表 */
function getList() {
    loading.value = true

    // 处理时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
        queryParams.value.start_time = dateRange.value[0]
        queryParams.value.end_time = dateRange.value[1]
    } else {
        queryParams.value.start_time = undefined
        queryParams.value.end_time = undefined
    }

    // 构建实际的查询参数，过滤掉空值
    const actualParams = {}
    Object.keys(queryParams.value).forEach(key => {
        const value = queryParams.value[key]
        if (value !== undefined && value !== null && value !== '') {
            actualParams[key] = value
        }
    })

    // Audio API removed - setting empty data
    audioList.value = []
    total.value = 0
    loading.value = false
}

/** 获取法宣数据 */
function getFaxuanData() {
    loading.value = true

    // 单位属性映射：将英文值转换为中文值
    const unitTypeMap = {
        'cityGov': '市级政府',
        'districtGov': '区级政府',
        'university': '高校',
        'stateEnterprise': '市属国企'
    }

    // 构建请求参数 - 根据用户要求的数据格式
    const requestData = {
        unit_property: queryParams.value.unitType ? unitTypeMap[queryParams.value.unitType] || "" : "",
        industry_system: queryParams.value.industry_system || "",
        publish_time_start: (dateRange.value && dateRange.value.length === 2) ? dateRange.value[0] : "",
        publish_time_end: (dateRange.value && dateRange.value.length === 2) ? dateRange.value[1] : "",
        crawl_channel: queryParams.value.source || "",
        legal_content_type: queryParams.value.laws || "",
        people_scale: queryParams.value.people_num || "",
        target_group: queryParams.value.accept_people || ""
    }

    // 保存筛选参数供详情页面使用
    lastFilterParams.value = { ...requestData }

    getFilterArticlesSum(requestData).then(response => {
        if (response.status === 'success') {
            faxuanData.value = response.data || []
            total.value = response.total || 0
        } else {
            proxy.$modal.msgError('获取数据失败：' + (response.message || '未知错误'))
            faxuanData.value = []
            total.value = 0
        }
    }).catch(error => {
        console.error('获取法宣数据失败:', error)
        proxy.$modal.msgError('获取数据失败，请稍后重试')
        faxuanData.value = []
        total.value = 0
    }).finally(() => {
        loading.value = false
    })
}


/** 获取状态类型 */
function getStatusType(status) {
    const statusMap = {
        'uploaded': 'info',
        'processing': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'exception': 'warning'  // 异常状态
    }
    return statusMap[status] || 'info'
}

/** 获取状态文本 */
function getStatusText(status) {
    const statusMap = {
        'uploaded': '未处理',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '处理失败',
        'exception': '异常'  // 异常状态
    }
    return statusMap[status] || '未知状态'
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1
    getFaxuanData()
}

/** 重置按钮操作 */
function resetQuery() {
    dateRange.value = []
    // 清空得分区间筛选条件
    queryParams.value.min_score = undefined
    queryParams.value.max_score = undefined
    // 重置新增字段
    moduleSelection.value = ''
    queryParams.value.unitType = undefined
    queryParams.value.industry_system = undefined
    queryParams.value.file_name = undefined
    queryParams.value.source = undefined
    queryParams.value.laws = undefined
    queryParams.value.people_num = undefined
    queryParams.value.form = undefined
    queryParams.value.accept_people = undefined
    // 重置单位属性相关的显示状态
    showRadioButtons.value = false
    selectedUnitTypeDisplay.value = ''
    proxy.resetForm("queryRef")
    handleQuery()
}

/** 处理模块选择切换 */
function handleModuleChange(module) {
    moduleSelection.value = module
    if (module === 'unit') {
        // 选择单位属性模块时，清空行业系统选择
        queryParams.value.industry_system = undefined
    } else if (module === 'industry') {
        // 选择行业系统模块时，清空单位属性选择
        queryParams.value.unitType = undefined
    }
}

/** 处理下拉框点击事件 */
function handleSelectClick() {
    // 点击下拉框时，隐藏下拉框，显示单选按钮
    showRadioButtons.value = true
}

/** 处理单选框点击事件 - 支持点击两次取消选中 */
function handleRadioClick(value) {
    if (queryParams.value.unitType === value) {
        // 如果当前已选中该值，则取消选中
        queryParams.value.unitType = undefined
        moduleSelection.value = ''
        selectedUnitTypeDisplay.value = ''
        // 隐藏单选按钮，显示下拉框
        showRadioButtons.value = false
    } else {
        // 否则选中该值
        queryParams.value.unitType = value
        moduleSelection.value = 'unit'
        // 清空行业系统选择
        queryParams.value.industry_system = undefined
        // 更新显示文本
        const unitTypeMap = {
            'cityGov': '市级政府',
            'districtGov': '区级政府',
            'university': '高校',
            'stateEnterprise': '市属国企'
        }
        selectedUnitTypeDisplay.value = unitTypeMap[value]
        // 隐藏单选按钮，显示下拉框
        showRadioButtons.value = false
    }
}

/** 处理单位类型单选框变化 */
function handleUnitTypeChange() {
    // 当选择单位类型时，自动设置模块选择为单位属性
    moduleSelection.value = 'unit'
    // 清空行业系统选择
    queryParams.value.industry_system = undefined
}

/** 处理表格排序 */
function handleSort(prop, order) {
    // 对当前显示的数据进行排序
    audioList.value.sort((a, b) => {
        let aVal = a[prop]
        let bVal = b[prop]

        // 处理数字类型的排序
        if (typeof aVal === 'string' && !isNaN(aVal)) {
            aVal = parseFloat(aVal)
        }
        if (typeof bVal === 'string' && !isNaN(bVal)) {
            bVal = parseFloat(bVal)
        }

        if (order === 'asc') {
            return aVal > bVal ? 1 : -1
        } else {
            return aVal < bVal ? 1 : -1
        }
    })
}

/** 处理文件名称输入 */
function handleFileNameInput(value) {
    queryParams.value.file_name = value.trim()
}

/** 处理上传人输入 */
function handleUserNameInput(value) {
    queryParams.value.user_name = value.trim()
}

/** 上传按钮操作 */
function handleUpload() {
    fileList.value = []
    uploadDialogVisible.value = true
}


/** 选择变化处理 */
function handleSelectionChange(selection) {
    console.log('handleSelectionChange called with selection:', selection)
    selectedAudios.value = selection
    console.log('selectedAudios updated to:', selectedAudios.value)
}



/** 详情按钮操作 */
function handleDetail(row) {
    // 构建详情接口参数
    const detailParams = {
        unit_name: row.unit || "",
        ...lastFilterParams.value
    }

    // 调用详情接口
    getFilterArticlesDetail(detailParams).then(response => {
        if (response.status === 'success') {
            // 跳转到详情页面，并传递数据
            proxy.$router.push({
                name: 'FaxuanDetail',
                params: { id: row.unit || 'unknown' },
                query: {
                    t: Date.now(),
                    data: JSON.stringify(response.data)
                }
            })
        } else {
            proxy.$modal.msgError('获取详情数据失败：' + (response.message || '未知错误'))
        }
    }).catch(error => {
        console.error('获取详情数据失败:', error)
        proxy.$modal.msgError('获取详情数据失败，请稍后重试')
    })
}

/** 删除按钮操作 */
function handleDelete(row) {
    // Audio API removed - delete functionality disabled
    proxy.$modal.msgError('删除功能暂不可用')
}

// 页面加载时自动获取法宣数据
getFaxuanData()
</script>

<style scoped>

.player-controls {
    margin-right: 20px;
}

.player-info {
    flex: 1;
    min-width: 0;
}

.time-info {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;
    margin-bottom: 10px;
}

.current-time,
.total-time {
    font-family: 'Courier New', monospace;
    min-width: 40px;
}

.separator {
    margin: 0 8px;
}

.progress-container {
    width: 100%;
}

.progress-slider {
    margin: 0;
}

.progress-slider :deep(.el-slider__runway) {
    height: 6px;
    background-color: #e4e7ed;
}

.progress-slider :deep(.el-slider__bar) {
    height: 6px;
    background-color: #409eff;
}

.progress-slider :deep(.el-slider__button) {
    width: 16px;
    height: 16px;
    border: 2px solid #409eff;
    background-color: #fff;
}

.progress-slider :deep(.el-slider__button:hover) {
    transform: scale(1.2);
}
</style>
