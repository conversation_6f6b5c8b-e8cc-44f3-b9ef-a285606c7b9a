<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px" style="margin-left: 50px; position: relative;">
<!--            第一行 - 单位属性模块-->
            <el-form-item label="单位属性" prop="unitType">
                <el-select
                    v-model="queryParams.unitType"
                    placeholder="全部"
                    clearable
                    style="width: 120px"
                    :disabled="moduleSelection === 'industry'"
                    @change="handleModuleChange('unit')">
                    <el-option label="市级政府" value="cityGov"></el-option>
                    <el-option label="区级政府" value="districtGov"></el-option>
                    <el-option label="高校" value="university"></el-option>
                    <el-option label="市属国企" value="stateEnterprise"></el-option>
                </el-select>
            </el-form-item>
<!--            第二行 - 行业系统模块-->
            <el-form-item label="行业系统" prop="industry_system">
                <el-select
                    v-model="queryParams.industry_system"
                    placeholder="全部"
                    clearable
                    style="width: 120px"
                    :disabled="moduleSelection === 'unit'"
                    @change="handleModuleChange('industry')">
                    <el-option label="纪委监委系统" value="纪委监委系统"></el-option>
                    <el-option label="组织系统" value="组织系统"></el-option>
                    <el-option label="宣传系统" value="宣传系统"></el-option>
                    <el-option label="社会工作系统" value="社会工作系统"></el-option>
                    <el-option label="网信系统" value="网信系统"></el-option>
                    <el-option label="机要保密系统" value="机要保密系统"></el-option>
                    <el-option label="档案馆系统" value="档案馆系统"></el-option>
                    <el-option label="法院系统" value="法院系统"></el-option>
                    <el-option label="检察院系统" value="检察院系统"></el-option>
                    <el-option label="发改委系统" value="发改委系统"></el-option>
                    <el-option label="教育系统" value="教育系统"></el-option>
                    <el-option label="科技系统" value="科技系统"></el-option>
                    <el-option label="工信系统" value="工信系统"></el-option>
                    <el-option label="公安系统" value="公安系统"></el-option>
                    <el-option label="民政系统" value="民政系统"></el-option>
                    <el-option label="司法系统" value="司法系统"></el-option>
                    <el-option label="财政系统" value="财政系统"></el-option>
                    <el-option label="人社系统" value="人社系统"></el-option>
                    <el-option label="规划资源系统" value="规划资源系统"></el-option>
                    <el-option label="生态环境系统" value="生态环境系统"></el-option>
                    <el-option label="建委系统" value="建委系统"></el-option>
                    <el-option label="房产系统" value="房产系统"></el-option>
                    <el-option label="交通运输系统" value="交通运输系统"></el-option>
                    <el-option label="水务系统" value="水务系统"></el-option>
                    <el-option label="城管系统" value="城管系统"></el-option>
                    <el-option label="绿化园林系统" value="绿化园林系统"></el-option>
                    <el-option label="农业农村系统" value="农业农村系统"></el-option>
                    <el-option label="商务系统" value="商务系统"></el-option>
                    <el-option label="文旅系统" value="文旅系统"></el-option>
                    <el-option label="卫健系统" value="卫健系统"></el-option>
                    <el-option label="退役军人事务系统" value="退役军人事务系统"></el-option>
                    <el-option label="应急系统" value="应急系统"></el-option>
                    <el-option label="审计系统" value="审计系统"></el-option>
                    <el-option label="国资委系统" value="国资委系统"></el-option>
                    <el-option label="数据局系统" value="数据局系统"></el-option>
                    <el-option label="市场监管系统" value="市场监管系统"></el-option>
                    <el-option label="体育系统" value="体育系统"></el-option>
                    <el-option label="统计系统" value="统计系统"></el-option>
                    <el-option label="信访系统" value="信访系统"></el-option>
                    <el-option label="民宗系统" value="民宗系统"></el-option>
                    <el-option label="国动办系统" value="国动办系统"></el-option>
                    <el-option label="金融监管系统" value="金融监管系统"></el-option>
                    <el-option label="税务系统" value="税务系统"></el-option>
                    <el-option label="消防系统" value="消防系统"></el-option>
                    <el-option label="工会系统" value="工会系统"></el-option>
                    <el-option label="团委系统" value="团委系统"></el-option>
                    <el-option label="妇联系统" value="妇联系统"></el-option>
                    <el-option label="红十字会系统" value="红十字会系统"></el-option>
                    <el-option label="工商联系统" value="工商联系统"></el-option>
                    <el-option label="残联系统" value="残联系统"></el-option>
                    <el-option label="高校系统" value="高校系统"></el-option>
                    <el-option label="国企系统" value="国企系统"></el-option>
                    <el-option label="其它系统" value="其它系统"></el-option>
                </el-select>
            </el-form-item>
<!--            第三行 - 时间-->
            <el-form-item label="时间" prop="dateRange">
                <el-date-picker
                    v-model="dateRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 185px"
                />
            </el-form-item>
            <el-form-item style="position: absolute; right: 50px;">
                <el-button type="primary" icon="Search" @click="handleQuery">筛选分析</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
            <br>
<!--            第四行 - 来源-->
            <el-form-item label="来源" prop="source">
                <el-select
                    v-model="queryParams.source"
                    placeholder="全部"
                    clearable
                    style="width: 120px">
                    <el-option label="微信公众号" value="微信公众号"></el-option>
                    <el-option label="官网" value="官网"></el-option>
                </el-select>
            </el-form-item>
<!--            第五行 - 所涉法律法规-->
            <el-form-item label="所涉法律法规" prop="laws" label-width="120px">
                <el-select
                    v-model="queryParams.laws"
                    placeholder="全部"
                    clearable
                    style="width: 120px">
                    <el-option label="宪法" value="宪法"></el-option>
                    <el-option label="民法典" value="民法典"></el-option>
                    <el-option label="行政处罚法" value="行政处罚法"></el-option>
                    <el-option label="行政复议法" value="行政复议法"></el-option>
                    <el-option label="安全生产法" value="安全生产法"></el-option>
                    <el-option label="突发事件应对法" value="突发事件应对法"></el-option>
                    <el-option label="反电信网络诈骗法" value="反电信网络诈骗法"></el-option>
                    <el-option label="妇女权益保障法" value="妇女权益保障法"></el-option>
                    <el-option label="未成年人保护法" value="未成年人保护法"></el-option>
                    <el-option label="国家安全法" value="国家安全法"></el-option>
                    <el-option label="其它法律" value="其它法律"></el-option>
                </el-select>
            </el-form-item>
<!--            第六行 - 参与人数-->
            <el-form-item label="参与人数" prop="people_num">
                <el-select
                    v-model="queryParams.people_num"
                    placeholder="全部"
                    clearable
                    style="width: 120px">
                    <el-option label="1-50人" value="1-50人"></el-option>
                    <el-option label="51-150人" value="51-150人"></el-option>
                    <el-option label="151-500人" value="151-500人"></el-option>
                    <el-option label="501-1000人" value="501-1000人"></el-option>
                    <el-option label="1000人以上" value="1000人以上"></el-option>
                </el-select>
            </el-form-item>
<!--            第七行 - 受众群体-->
            <el-form-item label="受众群体" prop="audience">
                <el-select
                    v-model="queryParams.audience"
                    placeholder="全部"
                    clearable
                    style="width: 120px">
                    <el-option label="青少年" value="青少年"></el-option>
                    <el-option label="居民" value="居民"></el-option>
                    <el-option label="企业人员" value="企业人员"></el-option>
                    <el-option label="老年人" value="老年人"></el-option>
                    <el-option label="国家工作人员" value="国家工作人员"></el-option>
                    <el-option label="其它人员" value="其它人员"></el-option>
                </el-select>
            </el-form-item>
        </el-form>

        <el-table
            v-loading="loading"
            :data="faxuanData"
            :row-key="(row) => row.unit"
            ref="tableRefs"
            @selection-change="handleSelectionChange"
        >
            <el-table-column label="单位" width="200" align="center" prop="unit"></el-table-column>
            <el-table-column prop="total_articles" width="160" align="center">
                <template #header>
                    <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                        <div style="flex: 1;"></div>
                        <span style="flex: 0 0 auto;">信息发布总量</span>
                        <div style="flex: 1; display: flex; justify-content: center; min-height: 40px;">
                            <div style="display: flex; flex-direction: column; align-items: center; gap: 2px; height: 100%; justify-content: center;">
                                <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowUp /></el-icon>
                                 </el-button>
                                 <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowDown /></el-icon>
                                 </el-button>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
<!--            <el-table-column prop="id" label="音频ID" width="100" align="center"></el-table-column>-->
            <el-table-column prop="total_views" width="100" align="center">
                <template #header>
                    <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                        <div style="flex: 1;"></div>
                        <span style="flex: 0 0 auto;">总阅读量</span>
                        <div style="flex: 1; display: flex; justify-content: center; min-height: 30px;">
                             <div style="display: flex; flex-direction: column; align-items: center; gap: 2px; height: 100%; justify-content: center;">
                                 <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                      <el-icon size="10"><ArrowUp /></el-icon>
                                  </el-button>
                                  <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                      <el-icon size="10"><ArrowDown /></el-icon>
                                  </el-button>
                             </div>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <el-table-column prop="max_article_views" width="140" align="center">
                <template #header>
                    <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                        <div style="flex: 1;"></div>
                        <span style="flex: 0 0 auto;">单篇最高阅读量</span>
                        <div style="flex: 1; display: flex; justify-content: center; min-height: 30px;">
                             <div style="display: flex; flex-direction: column; align-items: center; gap: 2px; height: 100%; justify-content: center;">
                                <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowUp /></el-icon>
                                 </el-button>
                                 <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowDown /></el-icon>
                                 </el-button>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="total_likes" width="100">
                <template #header>
                    <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                        <div style="flex: 1;"></div>
                        <span style="flex: 0 0 auto;">总点赞量</span>
                        <div style="flex: 1; display: flex; justify-content: center; min-height: 30px;">
                             <div style="display: flex; flex-direction: column; align-items: center; gap: 2px; height: 100%; justify-content: center;">
                                <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowUp /></el-icon>
                                 </el-button>
                                 <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowDown /></el-icon>
                                 </el-button>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="max_article_likes" width="140">
                <template #header>
                    <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                        <div style="flex: 1;"></div>
                        <span style="flex: 0 0 auto;">单篇最高点赞量</span>
                        <div style="flex: 1; display: flex; justify-content: center; min-height: 30px;">
                             <div style="display: flex; flex-direction: column; align-items: center; gap: 2px; height: 100%; justify-content: center;">
                                <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowUp /></el-icon>
                                 </el-button>
                                 <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowDown /></el-icon>
                                 </el-button>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="total_comments" width="100">
                <template #header>
                    <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                        <div style="flex: 1;"></div>
                        <span style="flex: 0 0 auto;">总评论量</span>
                        <div style="flex: 1; display: flex; justify-content: center; min-height: 30px;">
                             <div style="display: flex; flex-direction: column; align-items: center; gap: 2px; height: 100%; justify-content: center;">
                                <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowUp /></el-icon>
                                 </el-button>
                                 <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowDown /></el-icon>
                                 </el-button>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="max_article_comments" width="140">
                <template #header>
                    <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                        <div style="flex: 1;"></div>
                        <span style="flex: 0 0 auto;">单篇最高评论量</span>
                        <div style="flex: 1; display: flex; justify-content: center; min-height: 30px;">
                             <div style="display: flex; flex-direction: column; align-items: center; gap: 2px; height: 100%; justify-content: center;">
                                <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowUp /></el-icon>
                                 </el-button>
                                 <el-button size="small" type="text" style="padding: 0; height: 12px; line-height: 12px; display: flex; align-items: center; justify-content: center; width: 16px;">
                                     <el-icon size="10"><ArrowDown /></el-icon>
                                 </el-button>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
                               v-hasPermi="['system:audio:detail']">详情
                    </el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                               v-hasPermi="['system:audio:remove']">删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getFaxuanData"
        />
    </div>
</template>

<script setup name="Audio">
import {getToken} from "@/utils/auth"
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { getFilterArticles, getFilterArticlesSum, getFilterArticlesDetail } from '@/api/business/faxuan'

const {proxy} = getCurrentInstance()

const tableRefs = ref(null)
const audioList = ref([])
const faxuanData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const uploadDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const fileList = ref([])
const dateRange = ref([])
const total = ref(0)
const selectedAudios = ref([])
const audioPlayerVisible = ref(false)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const progressValue = ref(0)
const isDragging = ref(false)
let currentAudio = null

// 上传配置
const uploadAction = ref(import.meta.env.VITE_APP_BASE_API + '/swapi/audio/files/upload')
const uploadHeaders = ref({
    Authorization: 'Bearer ' + getToken()
})

const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        file_name: undefined,
        user_name: undefined,
        start_time: undefined,
        end_time: undefined,
        min_score: undefined,
        max_score: undefined,
        unitType: undefined,
        industry_system: undefined,
        source: undefined,
        laws: undefined,
        people_num: undefined,
        form: undefined,
        accept_people: undefined
    }
})

// 模块选择状态：'unit' 表示选择了单位属性模块，'industry' 表示选择了行业系统模块
const moduleSelection = ref('')

// 控制单选按钮显示状态
const showRadioButtons = ref(false)

// 下拉框显示文本
const selectedUnitTypeDisplay = ref('')

// 保存上次筛选分析时的参数
const lastFilterParams = ref({
    unit_property: "",
    industry_system: "",
    publish_time_start: "",
    publish_time_end: "",
    crawl_channel: "",
    legal_content_type: "",
    people_scale: "",
    target_group: ""
})

const {queryParams} = toRefs(data)

/** 查询音频列表 */
function getList() {
    loading.value = true

    // 处理时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
        queryParams.value.start_time = dateRange.value[0]
        queryParams.value.end_time = dateRange.value[1]
    } else {
        queryParams.value.start_time = undefined
        queryParams.value.end_time = undefined
    }

    // 构建实际的查询参数，过滤掉空值
    const actualParams = {}
    Object.keys(queryParams.value).forEach(key => {
        const value = queryParams.value[key]
        if (value !== undefined && value !== null && value !== '') {
            actualParams[key] = value
        }
    })

    // Audio API removed - setting empty data
    audioList.value = []
    total.value = 0
    loading.value = false
}

/** 获取法宣数据 */
function getFaxuanData() {
    loading.value = true

    // 单位属性映射：将英文值转换为中文值
    const unitTypeMap = {
        'cityGov': '市级政府',
        'districtGov': '区级政府',
        'university': '高校',
        'stateEnterprise': '市属国企'
    }

    // 构建请求参数 - 根据用户要求的数据格式
    const requestData = {
        unit_property: queryParams.value.unitType ? unitTypeMap[queryParams.value.unitType] || "" : "",
        industry_system: queryParams.value.industry_system || "",
        publish_time_start: (dateRange.value && dateRange.value.length === 2) ? dateRange.value[0] : "",
        publish_time_end: (dateRange.value && dateRange.value.length === 2) ? dateRange.value[1] : "",
        crawl_channel: queryParams.value.source || "",
        legal_content_type: queryParams.value.laws || "",
        people_scale: queryParams.value.people_num || "",
        target_group: queryParams.value.accept_people || ""
    }

    // 保存筛选参数供详情页面使用
    lastFilterParams.value = { ...requestData }

    getFilterArticlesSum(requestData).then(response => {
        if (response.status === 'success') {
            faxuanData.value = response.data || []
            total.value = response.total || 0
        } else {
            proxy.$modal.msgError('获取数据失败：' + (response.message || '未知错误'))
            faxuanData.value = []
            total.value = 0
        }
    }).catch(error => {
        console.error('获取法宣数据失败:', error)
        proxy.$modal.msgError('获取数据失败，请稍后重试')
        faxuanData.value = []
        total.value = 0
    }).finally(() => {
        loading.value = false
    })
}


/** 获取状态类型 */
function getStatusType(status) {
    const statusMap = {
        'uploaded': 'info',
        'processing': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'exception': 'warning'  // 异常状态
    }
    return statusMap[status] || 'info'
}

/** 获取状态文本 */
function getStatusText(status) {
    const statusMap = {
        'uploaded': '未处理',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '处理失败',
        'exception': '异常'  // 异常状态
    }
    return statusMap[status] || '未知状态'
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1
    getFaxuanData()
}

/** 重置按钮操作 */
function resetQuery() {
    dateRange.value = []
    // 清空得分区间筛选条件
    queryParams.value.min_score = undefined
    queryParams.value.max_score = undefined
    // 重置新增字段
    moduleSelection.value = ''
    queryParams.value.unitType = undefined
    queryParams.value.industry_system = undefined
    queryParams.value.file_name = undefined
    queryParams.value.source = undefined
    queryParams.value.laws = undefined
    queryParams.value.people_num = undefined
    queryParams.value.form = undefined
    queryParams.value.accept_people = undefined
    // 重置单位属性相关的显示状态
    showRadioButtons.value = false
    selectedUnitTypeDisplay.value = ''
    proxy.resetForm("queryRef")
    handleQuery()
}

/** 处理模块选择切换 */
function handleModuleChange(module) {
    moduleSelection.value = module
    if (module === 'unit') {
        // 选择单位属性模块时，清空行业系统选择
        queryParams.value.industry_system = undefined
    } else if (module === 'industry') {
        // 选择行业系统模块时，清空单位属性选择
        queryParams.value.unitType = undefined
    }
}

/** 处理下拉框点击事件 */
function handleSelectClick() {
    // 点击下拉框时，隐藏下拉框，显示单选按钮
    showRadioButtons.value = true
}

/** 处理单选框点击事件 - 支持点击两次取消选中 */
function handleRadioClick(value) {
    if (queryParams.value.unitType === value) {
        // 如果当前已选中该值，则取消选中
        queryParams.value.unitType = undefined
        moduleSelection.value = ''
        selectedUnitTypeDisplay.value = ''
        // 隐藏单选按钮，显示下拉框
        showRadioButtons.value = false
    } else {
        // 否则选中该值
        queryParams.value.unitType = value
        moduleSelection.value = 'unit'
        // 清空行业系统选择
        queryParams.value.industry_system = undefined
        // 更新显示文本
        const unitTypeMap = {
            'cityGov': '市级政府',
            'districtGov': '区级政府',
            'university': '高校',
            'stateEnterprise': '市属国企'
        }
        selectedUnitTypeDisplay.value = unitTypeMap[value]
        // 隐藏单选按钮，显示下拉框
        showRadioButtons.value = false
    }
}

/** 处理单位类型单选框变化 */
function handleUnitTypeChange() {
    // 当选择单位类型时，自动设置模块选择为单位属性
    moduleSelection.value = 'unit'
    // 清空行业系统选择
    queryParams.value.industry_system = undefined
}

/** 处理表格排序 */
function handleSort(prop, order) {
    // 对当前显示的数据进行排序
    audioList.value.sort((a, b) => {
        let aVal = a[prop]
        let bVal = b[prop]

        // 处理数字类型的排序
        if (typeof aVal === 'string' && !isNaN(aVal)) {
            aVal = parseFloat(aVal)
        }
        if (typeof bVal === 'string' && !isNaN(bVal)) {
            bVal = parseFloat(bVal)
        }

        if (order === 'asc') {
            return aVal > bVal ? 1 : -1
        } else {
            return aVal < bVal ? 1 : -1
        }
    })
}

/** 处理文件名称输入 */
function handleFileNameInput(value) {
    queryParams.value.file_name = value.trim()
}

/** 处理上传人输入 */
function handleUserNameInput(value) {
    queryParams.value.user_name = value.trim()
}

/** 上传按钮操作 */
function handleUpload() {
    fileList.value = []
    uploadDialogVisible.value = true
}


/** 选择变化处理 */
function handleSelectionChange(selection) {
    console.log('handleSelectionChange called with selection:', selection)
    selectedAudios.value = selection
    console.log('selectedAudios updated to:', selectedAudios.value)
}



/** 详情按钮操作 */
function handleDetail(row) {
    // 构建详情接口参数
    const detailParams = {
        unit_name: row.unit || "",
        ...lastFilterParams.value
    }

    // 调用详情接口
    getFilterArticlesDetail(detailParams).then(response => {
        if (response.status === 'success') {
            // 跳转到详情页面，并传递数据
            proxy.$router.push({
                name: 'FaxuanDetail',
                params: { id: row.unit || 'unknown' },
                query: {
                    t: Date.now(),
                    data: JSON.stringify(response.data)
                }
            })
        } else {
            proxy.$modal.msgError('获取详情数据失败：' + (response.message || '未知错误'))
        }
    }).catch(error => {
        console.error('获取详情数据失败:', error)
        proxy.$modal.msgError('获取详情数据失败，请稍后重试')
    })
}

/** 删除按钮操作 */
function handleDelete(row) {
    // Audio API removed - delete functionality disabled
    proxy.$modal.msgError('删除功能暂不可用')
}

// 页面加载时自动获取法宣数据
getFaxuanData()
</script>

<style scoped>

.player-controls {
    margin-right: 20px;
}

.player-info {
    flex: 1;
    min-width: 0;
}

.time-info {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;
    margin-bottom: 10px;
}

.current-time,
.total-time {
    font-family: 'Courier New', monospace;
    min-width: 40px;
}

.separator {
    margin: 0 8px;
}

.progress-container {
    width: 100%;
}

.progress-slider {
    margin: 0;
}

.progress-slider :deep(.el-slider__runway) {
    height: 6px;
    background-color: #e4e7ed;
}

.progress-slider :deep(.el-slider__bar) {
    height: 6px;
    background-color: #409eff;
}

.progress-slider :deep(.el-slider__button) {
    width: 16px;
    height: 16px;
    border: 2px solid #409eff;
    background-color: #fff;
}

.progress-slider :deep(.el-slider__button:hover) {
    transform: scale(1.2);
}
</style>
