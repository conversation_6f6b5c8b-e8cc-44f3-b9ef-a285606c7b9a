<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px" style="margin-left: 50px; position: relative;">

        </el-form>

        <el-table :data="detailData" :loading="loading" style="width: 100%; display: flex;">
<!--            <el-table-column type="selection" width="150" align="center" :selectable="isRowSelectable" :reserve-selection="true"></el-table-column>-->
            <el-table-column label="时间" width="160" align="center" prop="publish_time"></el-table-column>
            <el-table-column label="单位" width="100" align="center" prop="unit_name"></el-table-column>
<!--            <el-table-column prop="id" label="音频ID" width="100" align="center"></el-table-column>-->
            <el-table-column label="文章名称" width="350" align="center" prop="article_title"></el-table-column>

            <el-table-column label="文章阅读量" prop="view_count" width="100" align="center"></el-table-column>
            <el-table-column label="文章点赞量" align="center" prop="likes" width="100"></el-table-column>
            <el-table-column label="文章评论量" align="center" prop="comments" width="100"></el-table-column>
            <el-table-column label="所涉法律法规" align="center" prop="legal_content_type" width="200"></el-table-column>
            <el-table-column label="受众群体" align="center" prop="target_group" width="120"></el-table-column>
            <el-table-column label="参与人数" align="center" prop="people_scale" width="100"></el-table-column>
            <el-table-column label="来源" align="center" prop="crawl_channel" min-width="80"></el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
        />
    </div>
</template>

<script setup name="Audio">
// Audio API functions removed
import {getToken} from "@/utils/auth"
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'

const {proxy} = getCurrentInstance()
const route = useRoute()
const tableRefs = ref(null)
const loading = ref(false)
const showSearch = ref(false)
const detailData = ref([])
const total = ref(0)

// 从路由参数中获取数据
try {
    const dataParam = route.query.data
    if (dataParam) {
        const parsedData = JSON.parse(dataParam)
        detailData.value = parsedData || []
        total.value = detailData.value.length
    }
} catch (error) {
    console.error('解析详情数据失败:', error)
    detailData.value = []
    total.value = 0
}

const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10
    }
})

const {queryParams} = toRefs(data)



/** 多选框选中数据 */
function handleSelectionChange(selection) {
    // 处理表格行选择变化
    console.log('选中的行:', selection)
}

/** 简化的获取列表函数 */
function getList() {
    // 详情页面不需要重新获取数据
    console.log('详情页面数据已通过路由参数传递')
}

</script>

<style scoped>



.player-controls {
    margin-right: 20px;
}

.player-info {
    flex: 1;
    min-width: 0;
}

.current-time,
.total-time {
    font-family: 'Courier New', monospace;
    min-width: 40px;
}

.separator {
    margin: 0 8px;
}

.progress-container {
    width: 100%;
}

.progress-slider {
    margin: 0;
}

.progress-slider :deep(.el-slider__runway) {
    height: 6px;
    background-color: #e4e7ed;
}

.progress-slider :deep(.el-slider__bar) {
    height: 6px;
    background-color: #409eff;
}

.progress-slider :deep(.el-slider__button) {
    width: 16px;
    height: 16px;
    border: 2px solid #409eff;
    background-color: #fff;
}

.progress-slider :deep(.el-slider__button:hover) {
    transform: scale(1.2);
}
</style>
