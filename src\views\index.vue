<template>
  <div class="dashboard-container">
    <!-- Header -->
    <div class="header">
      <h1 class="page-title">
        <img src="@/assets/logo/pufa_logo.png" alt="logo" />
        普法工作管理系统
      </h1>
      <div class="current-date">{{ currentDate }}</div>
    </div>

    <!-- Stats Cards -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="12">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-title">平台收集官方信息总数</div>
          <div class="stat-value">12,345</div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-title">今日增加数</div>
          <div class="stat-value">678</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Charts Grid -->
    <el-row :gutter="20" class="charts-grid">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">
              <span class="chart-title">各单位今日发布数</span>
              <el-button
                class="detail-btn"
                type="primary"
                size="small"
                @click="showDetail('barChart')"
                :icon="InfoFilled"
              >
                详情
              </el-button>
            </div>
          </template>
          <div ref="barChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">
              <span class="chart-title">本周各单位发布数统计</span>
              <el-button
                class="detail-btn"
                type="primary"
                size="small"
                @click="showDetail('horizontalBarChart')"
                :icon="InfoFilled"
              >
                详情
              </el-button>
            </div>
          </template>
          <div ref="horizontalBarChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="charts-grid">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">
              <span class="chart-title">各来源今日发布数</span>
              <el-button
                class="detail-btn"
                type="primary"
                size="small"
                @click="showDetail('pieChart')"
                :icon="InfoFilled"
              >
                详情
              </el-button>
            </div>
          </template>
          <div ref="pieChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">
              <span class="chart-title">各普法内容类型本季度提及数</span>
              <el-button
                class="detail-btn"
                type="primary"
                size="small"
                @click="showDetail('lineChart')"
                :icon="InfoFilled"
              >
                详情
              </el-button>
            </div>
          </template>
          <div ref="lineChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Detail Dialog -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="90%"
      :before-close="handleClose"
      class="detail-dialog"
      top="3vh"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <!-- 统计信息 -->
        <div class="stats-summary">
          <div class="stat-item">
            <span class="stat-label">总记录数</span>
            <span class="stat-value">{{ tableData.length }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">总文章数</span>
            <span class="stat-value">{{ totalArticles }}</span>
          </div>
        </div>

        <!-- 表格 -->
        <el-table
          :data="tableData"
          style="width: 100%"
          border
          stripe
          size="default"
          class="detail-table"
          :header-cell-style="{ background: '#f8f9fa', color: '#495057', fontWeight: '600' }"
          max-height="60vh"
        >
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="unitType" label="单位属性" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getUnitTypeColor(scope.row.unitType)" size="small">
                {{ scope.row.unitType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="unitName" label="单位名称" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              <div class="unit-name">
                <el-icon class="unit-icon"><OfficeBuilding /></el-icon>
                {{ scope.row.unitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="articleCount" label="文章数" width="90" align="center" sortable>
            <template #default="scope">
              <el-tag type="primary" size="small" class="article-count-tag">
                {{ scope.row.articleCount }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="lastPublishTime" label="最新发布时间" width="150" align="center" sortable />
          <el-table-column label="操作" width="80" align="center" fixed="right">
            <template #default="scope">
              <el-button type="primary" link size="small" @click="viewDetail(scope.row)" class="action-btn">
                <el-icon><View /></el-icon>
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Index">
import { ref, onMounted, nextTick, computed } from 'vue';
import * as echarts from 'echarts';
import { InfoFilled, View, OfficeBuilding } from '@element-plus/icons-vue';

const currentDate = ref('');

// Dialog related data
const dialogVisible = ref(false);
const dialogTitle = ref('');
const tableData = ref([]);

// Computed properties
const totalArticles = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.articleCount, 0);
});

// Function to format date
const formatDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  currentDate.value = `${year}年${month}月${day}日`;
};

// Chart element refs
const barChart = ref(null);
const horizontalBarChart = ref(null);
const pieChart = ref(null);
const lineChart = ref(null);

// Init Bar Chart
const initBarChart = () => {
  const chart = echarts.init(barChart.value);
  const option = {
    tooltip: {},
    xAxis: {
      data: ['市政府', '区政府', '国企', '高校']
    },
    yAxis: {},
    series: [{
      name: '发布数',
      type: 'bar',
      data: [11, 18, 15, 10],
      itemStyle: {
        color: '#5470c6'
      }
    }]
  };
  chart.setOption(option);
};

// Init Horizontal Bar Chart
const initHorizontalBarChart = () => {
  const chart = echarts.init(horizontalBarChart.value);
  const option = {
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    yAxis: {
      type: 'category',
      data: ['单位Z', '单位Y', '单位X', '单位W', '单位V']
    },
    xAxis: { type: 'value' },
    series: [{
      name: '发布数',
      type: 'bar',
      data: [180, 210, 240, 280, 320],
      itemStyle: {
        color: '#91cc75'
      }
    }]
  };
  chart.setOption(option);
};

// Init Pie Chart
const initPieChart = () => {
  const chart = echarts.init(pieChart.value);
  const option = {
    tooltip: { trigger: 'item' },
    series: [{
      name: '来源',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 1048, name: '来源1' },
        { value: 735, name: '来源2' },
        { value: 580, name: '来源3' },
        { value: 484, name: '来源4' },
        { value: 300, name: '来源5' }
      ]
    }]
  };
  chart.setOption(option);
};

// Init Line Chart
const initLineChart = () => {
  const chart = echarts.init(lineChart.value);
  const option = {
    tooltip: { trigger: 'axis' },
    legend: {
      top: '5%'
    },
    grid: {
      top: '15%'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月']
    },
    yAxis: { type: 'value' },
    series: [{
      name: '类型一',
      type: 'line',
      data: [150, 230, 224]
    }, {
      name: '类型二',
      type: 'line',
      data: [120, 182, 191]
    }]
  };
  chart.setOption(option);
};

// Mock data for different chart details
const chartDetailData = {
  barChart: [
    { unitType: '市政府', unitName: '市政府办公室', articleCount: 5, lastPublishTime: '2024-01-15 14:30' },
    { unitType: '市政府', unitName: '市发改委', articleCount: 3, lastPublishTime: '2024-01-15 13:20' },
    { unitType: '市政府', unitName: '市财政局', articleCount: 3, lastPublishTime: '2024-01-15 12:10' },
    { unitType: '区政府', unitName: '朝阳区政府', articleCount: 8, lastPublishTime: '2024-01-15 15:45' },
    { unitType: '区政府', unitName: '海淀区政府', articleCount: 6, lastPublishTime: '2024-01-15 14:15' },
    { unitType: '区政府', unitName: '西城区政府', articleCount: 4, lastPublishTime: '2024-01-15 13:30' },
    { unitType: '国企', unitName: '国家电网', articleCount: 7, lastPublishTime: '2024-01-15 16:20' },
    { unitType: '国企', unitName: '中石油', articleCount: 5, lastPublishTime: '2024-01-15 15:10' },
    { unitType: '国企', unitName: '中石化', articleCount: 3, lastPublishTime: '2024-01-15 14:40' },
    { unitType: '高校', unitName: '清华大学', articleCount: 4, lastPublishTime: '2024-01-15 16:00' },
    { unitType: '高校', unitName: '北京大学', articleCount: 3, lastPublishTime: '2024-01-15 15:30' },
    { unitType: '高校', unitName: '人民大学', articleCount: 3, lastPublishTime: '2024-01-15 14:50' }
  ],
  horizontalBarChart: [
    { unitType: '政府机关', unitName: '单位V', articleCount: 320, lastPublishTime: '2024-01-15 16:30' },
    { unitType: '政府机关', unitName: '单位W', articleCount: 280, lastPublishTime: '2024-01-15 15:20' },
    { unitType: '事业单位', unitName: '单位X', articleCount: 240, lastPublishTime: '2024-01-15 14:10' },
    { unitType: '事业单位', unitName: '单位Y', articleCount: 210, lastPublishTime: '2024-01-15 13:40' },
    { unitType: '国有企业', unitName: '单位Z', articleCount: 180, lastPublishTime: '2024-01-15 12:50' }
  ],
  pieChart: [
    { unitType: '官方网站', unitName: '来源1', articleCount: 1048, lastPublishTime: '2024-01-15 16:45' },
    { unitType: '微信公众号', unitName: '来源2', articleCount: 735, lastPublishTime: '2024-01-15 15:35' },
    { unitType: '官方微博', unitName: '来源3', articleCount: 580, lastPublishTime: '2024-01-15 14:25' },
    { unitType: '新闻媒体', unitName: '来源4', articleCount: 484, lastPublishTime: '2024-01-15 13:15' },
    { unitType: '其他平台', unitName: '来源5', articleCount: 300, lastPublishTime: '2024-01-15 12:05' }
  ],
  lineChart: [
    { unitType: '法律法规', unitName: '类型一', articleCount: 224, lastPublishTime: '2024-03-15 16:20' },
    { unitType: '案例分析', unitName: '类型二', articleCount: 191, lastPublishTime: '2024-03-15 15:10' },
    { unitType: '政策解读', unitName: '类型三', articleCount: 156, lastPublishTime: '2024-03-15 14:30' },
    { unitType: '普法宣传', unitName: '类型四', articleCount: 128, lastPublishTime: '2024-03-15 13:45' }
  ]
};

// Chart titles mapping
const chartTitles = {
  barChart: '各单位今日发布内容详情',
  horizontalBarChart: '本周各单位发布内容详情',
  pieChart: '各来源今日发布内容详情',
  lineChart: '各普法内容类型本季度详情'
};

// Show detail dialog
const showDetail = (chartType) => {
  dialogTitle.value = chartTitles[chartType];
  tableData.value = chartDetailData[chartType] || [];
  dialogVisible.value = true;
};

// Handle dialog close
const handleClose = (done) => {
  dialogVisible.value = false;
  if (done) done();
};

// Get unit type color
const getUnitTypeColor = (unitType) => {
  const colorMap = {
    '市政府': 'danger',
    '区政府': 'warning',
    '国企': 'success',
    '高校': 'info',
    '政府机关': 'danger',
    '事业单位': 'warning',
    '国有企业': 'success',
    '官方网站': 'primary',
    '微信公众号': 'success',
    '官方微博': 'info',
    '新闻媒体': 'warning',
    '其他平台': '',
    '法律法规': 'danger',
    '案例分析': 'warning',
    '政策解读': 'success',
    '普法宣传': 'info'
  };
  return colorMap[unitType] || 'primary';
};

// View detail action
const viewDetail = (row) => {
  console.log('查看详情:', row);
  // 这里可以添加查看详情的逻辑，比如跳转到详情页面
};

onMounted(() => {
  formatDate();
  setInterval(formatDate, 1000 * 60 * 60); // Update every hour is enough for date
  nextTick(() => {
    initBarChart();
    initHorizontalBarChart();
    initPieChart();
    initLineChart();
  });
});
</script>

<style scoped lang="scss">
.dashboard-container {
  padding: 32px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
}

.page-title img {
  height: 28px; /* Match font-size of title */
  margin-right: 10px;
}

.current-date {
  font-size: 22px;
  color: #606266;
  font-weight: 500;
  letter-spacing: 1px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 12px;
  .stat-title {
    font-size: 16px;
    color: #909399;
    margin-bottom: 12px;
  }
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
  }
}

.charts-grid {
  margin-top: 20px;
  margin-bottom: 20px;
}

.chart-card {
  border-radius: 12px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafbfc;
  border-radius: 8px 8px 0 0;
  margin: -20px -20px 16px -20px;
  padding: 16px 20px;
  border-bottom: 2px solid #e1e8ed;
}

.chart-title {
  font-size: 18px;
  font-weight: 800;
  color: #1a202c;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.detail-btn {
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  background: #409eff;
  border: 1px solid #409eff;
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;

  &:hover {
    background: #66b1ff;
    border-color: #66b1ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
  }

  .el-icon {
    margin-right: 4px;
    font-size: 14px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 6px 12px;
    font-size: 12px;

    .el-icon {
      font-size: 12px;
      margin-right: 3px;
    }
  }
}





.chart-container {
  width: 100%;
  height: 350px;
}

// Dialog styles
.detail-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    margin: 0;
    border-bottom: none;
  }

  :deep(.el-dialog__title) {
    font-size: 18px;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-dialog__headerbtn) {
    top: 20px;
    right: 20px;

    .el-dialog__close {
      color: white;
      font-size: 18px;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 0;
    background: #ffffff;
  }
}

.dialog-content {
  padding: 24px;
  background: #ffffff;
}

.stats-summary {
  display: flex;
  gap: 32px;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  border-radius: 12px;
  border: 1px solid #e1e8ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;

  .stat-label {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #667eea;
    text-shadow: 0 1px 2px rgba(102, 126, 234, 0.1);
  }
}

.unit-name {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 2px 0;

  .unit-icon {
    color: #667eea;
    font-size: 16px;
  }
}

.article-count-tag {
  font-weight: 600;
  border-radius: 14px;
  padding: 4px 10px;
}

.action-btn {
  font-weight: 500;
  padding: 6px 10px;

  .el-icon {
    margin-right: 4px;
  }
}

.detail-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e1e8ed;

  :deep(.el-table__header-wrapper) {
    .el-table__header {
      th {
        border-bottom: 2px solid #e1e8ed;
        font-size: 14px;
        padding: 16px 12px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        font-weight: 600;
        text-align: center;
      }
    }
  }

  :deep(.el-table__body-wrapper) {
    .el-table__body {
      td {
        padding: 14px 12px;
        border-bottom: 1px solid #f0f0f0;
        font-size: 13px;
        vertical-align: middle;
        line-height: 1.5;
      }
    }
  }

  :deep(.el-table__row) {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f9ff !important;
      box-shadow: inset 0 0 0 1px rgba(102, 126, 234, 0.2);
    }

    &:nth-child(even) {
      background-color: #fafbfc;
    }
  }

  :deep(.el-tag) {
    border-radius: 12px;
    font-weight: 600;
    font-size: 12px;
    padding: 4px 10px;
    border: none;
    margin: 2px 0;
  }

  :deep(.el-button.is-link) {
    color: #667eea;
    font-weight: 500;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      color: #764ba2;
      background-color: rgba(102, 126, 234, 0.1);
      transform: translateY(-1px);
    }

    .el-icon {
      margin-right: 3px;
      font-size: 12px;
    }
  }

  // 序号列样式
  :deep(.el-table__column--selection) {
    .cell {
      padding: 0;
    }
  }
}
</style>
